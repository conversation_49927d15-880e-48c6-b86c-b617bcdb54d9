#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计API视图
提供统计数据的REST API接口
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.conf import settings
from django.core.paginator import Paginator
from django.shortcuts import render
from .services import (
    realtime_stats_service,
    performance_analysis_service,
    cronjob_monitoring_service
)
from .reports import (
    daily_report_generator,
    weekly_report_generator,
    alert_report_generator
)

logger = logging.getLogger(__name__)


def api_response(data: Any = None, error: str = None, status: int = 200) -> JsonResponse:
    """统一的API响应格式"""
    response_data = {
        'timestamp': timezone.now().isoformat(),
        'success': error is None
    }
    
    if error:
        response_data['error'] = error
        status = 400 if status == 200 else status
    else:
        response_data['data'] = data
    
    return JsonResponse(response_data, status=status)


@require_http_methods(["GET"])
def realtime_dashboard(request):
    """实时监控面板数据API"""
    try:
        data = realtime_stats_service.get_realtime_dashboard_data()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get realtime dashboard data: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def command_trends(request):
    """指令执行趋势API"""
    try:
        days = int(request.GET.get('days', 7))
        if days < 1 or days > 90:
            return api_response(error="Days parameter must be between 1 and 90")
        
        data = realtime_stats_service.get_command_trends(days=days)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid days parameter")
    except Exception as e:
        logger.error(f"Failed to get command trends: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def user_activity_stats(request):
    """用户活动统计API"""
    try:
        days = int(request.GET.get('days', 7))
        if days < 1 or days > 90:
            return api_response(error="Days parameter must be between 1 and 90")

        data = realtime_stats_service.get_user_activity_stats(days=days)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid days parameter")
    except Exception as e:
        logger.error(f"Failed to get user activity stats: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def activity_logs(request):
    """实时活动日志API"""
    try:
        limit = int(request.GET.get('limit', 20))  # 默认改为20条
        if limit < 1 or limit > 50:
            return api_response(error="Limit parameter must be between 1 and 50")

        data = realtime_stats_service.get_activity_logs(limit=limit)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid limit parameter")
    except Exception as e:
        logger.error(f"Failed to get activity logs: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_list(request):
    """定时任务列表API"""
    try:
        data = realtime_stats_service.get_cronjob_list()

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except Exception as e:
        logger.error(f"Failed to get cronjob list: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def comprehensive_cronjob_status(request):
    """综合定时任务状态API（包含用户任务）"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:  # 最多7天
            return api_response(error="Hours parameter must be between 1 and 168")

        from app01.statistics.services import cronjob_monitoring_service
        data = cronjob_monitoring_service.get_comprehensive_cronjob_status(hours=hours)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get comprehensive cronjob status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def performance_metrics(request):
    """性能指标API"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:  # 最多7天
            return api_response(error="Hours parameter must be between 1 and 168")
        
        data = performance_analysis_service.get_performance_metrics(hours=hours)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def slow_queries_analysis(request):
    """慢查询分析API"""
    try:
        threshold = float(request.GET.get('threshold', 1.0))
        if threshold < 0.1 or threshold > 60:
            return api_response(error="Threshold must be between 0.1 and 60 seconds")
        
        data = performance_analysis_service.get_slow_queries_analysis(threshold_seconds=threshold)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid threshold parameter")
    except Exception as e:
        logger.error(f"Failed to analyze slow queries: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_status(request):
    """定时任务状态API"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:
            return api_response(error="Hours parameter must be between 1 and 168")
        
        data = cronjob_monitoring_service.get_cronjob_status(hours=hours)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get cronjob status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_health_report(request):
    """定时任务健康报告API"""
    try:
        data = cronjob_monitoring_service.get_job_health_report()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get cronjob health report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def daily_report(request):
    """日报API"""
    try:
        date_str = request.GET.get('date')
        date = None
        
        if date_str:
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return api_response(error="Invalid date format. Use YYYY-MM-DD")
        
        format_type = request.GET.get('format', 'json')
        
        if format_type == 'html':
            html_content = daily_report_generator.generate_daily_report_html(date)
            return HttpResponse(html_content, content_type='text/html')
        else:
            data = daily_report_generator.generate_daily_report(date)
            
            if 'error' in data:
                return api_response(error=data['error'])
            
            return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate daily report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def weekly_report(request):
    """周报API"""
    try:
        week_start_str = request.GET.get('week_start')
        week_start = None
        
        if week_start_str:
            try:
                week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
            except ValueError:
                return api_response(error="Invalid week_start format. Use YYYY-MM-DD")
        
        data = weekly_report_generator.generate_weekly_report(week_start)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate weekly report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def alert_report(request):
    """告警报告API"""
    try:
        data = alert_report_generator.generate_alert_report()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate alert report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def command_execution_records(request):
    """指令执行记录查询API"""
    try:
        from app01.models import CommandExecutionRecord
        
        # 查询参数
        user_id = request.GET.get('user_id')
        command_type = request.GET.get('command_type')
        success = request.GET.get('success')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        # 构建查询
        queryset = CommandExecutionRecord.objects.all()
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if command_type:
            queryset = queryset.filter(command_type=command_type)
        
        if success is not None:
            success_bool = success.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(success=success_bool)
        
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                return api_response(error="Invalid start_date format. Use YYYY-MM-DD")
        
        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                queryset = queryset.filter(created_at__lt=end_dt)
            except ValueError:
                return api_response(error="Invalid end_date format. Use YYYY-MM-DD")
        
        # 分页
        paginator = Paginator(queryset.order_by('-created_at'), page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        records = []
        for record in page_obj:
            records.append({
                'execution_id': str(record.execution_id),
                'user_id': record.user_id,
                'user_email': record.user_email,
                'command_type': record.command_type,
                'raw_input': record.raw_input[:200],  # 限制长度
                'success': record.success,
                'processing_time': record.processing_time,
                'created_at': record.created_at.isoformat(),
                'error_message': record.error_message[:200] if record.error_message else None
            })
        
        return api_response({
            'records': records,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })
        
    except ValueError as e:
        return api_response(error=f"Invalid parameter: {e}")
    except Exception as e:
        logger.error(f"Failed to get command execution records: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def system_health_status(request):
    """系统健康状态API"""
    try:
        from app01.models import SystemHealthSnapshot
        
        # 获取最新的健康快照
        latest_snapshot = SystemHealthSnapshot.objects.first()
        
        if not latest_snapshot:
            return api_response(error="No health snapshot available")
        
        data = {
            'snapshot_time': latest_snapshot.snapshot_time.isoformat(),
            'overall_status': latest_snapshot.overall_status,
            'total_users_today': latest_snapshot.total_users_today,
            'active_users_now': latest_snapshot.active_users_now,
            'total_commands_today': latest_snapshot.total_commands_today,
            'success_rate_today': latest_snapshot.success_rate_today,
            'avg_response_time': latest_snapshot.avg_response_time,
            'system_load': latest_snapshot.system_load,
            'memory_usage': latest_snapshot.memory_usage,
            'cpu_usage': latest_snapshot.cpu_usage,
            'database_status': latest_snapshot.database_status,
            'redis_status': latest_snapshot.redis_status,
            'external_services_status': latest_snapshot.external_services_status,
            'error_count_last_hour': latest_snapshot.error_count_last_hour,
            'cronjobs_running': latest_snapshot.cronjobs_running,
            'cronjobs_failed_today': latest_snapshot.cronjobs_failed_today,
            'alerts': latest_snapshot.alerts
        }
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get system health status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def statistics_dashboard_page(request):
    """统计监控面板页面"""
    try:
        # 获取基础统计数据
        context = {
            'title': 'ChatBot AutoRelease 统计监控面板',
            'current_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
        }

        return render(request, 'statistics/dashboard.html', context)

    except Exception as e:
        logger.error(f"Failed to render statistics dashboard: {e}")
        return HttpResponse(f"页面加载失败: {str(e)}", status=500)
