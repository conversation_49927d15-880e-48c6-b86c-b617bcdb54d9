import json
import time
from icecream import ic
import requests
import sys
import os
import datetime
from pathlib import Path

# 导入装饰器
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()
from app01.statistics.decorators import track_cronjob_execution

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

os.environ["PYTHONIOENCODING"] = "utf-8"
# 定义一个 log_with_timestamp 函数，用于记录时间戳
def log_with_timestamp(message):
    # 获取当前时间
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 直接打印时间戳和消息
    try:
        print(f"{timestamp} - {message}")
    except UnicodeEncodeError:
        # 当遇到编码错误时，尝试使用ascii编码并忽略无法编码的字符
        print(f"{timestamp} - {str(message).encode('ascii', 'ignore').decode('ascii')}")


# 配置 icecream 的输出函数
ic.configureOutput(prefix='DEBUG:', outputFunction=log_with_timestamp)
# 定义一个 add_MR_label 函数，用于给 merge request 添加标签mport requests

import datetime
GITLAB_PRIVATE_TOKEN_OF_LIANG = "********************"
GITLAB_PRIVATE_TOKEN_OF_ZHULI = "********************"



def add_MR_label(repo, iid):
    # 定义基础的 URL
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    # 定义标签
    labels = "ChatbotAR_auto_merge"
    # 拼接添加标签的 URL
    add_label = f"{BASE_URL}{repo}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN_OF_LIANG}&labels={labels}"
    # 打印出 URL
    ic(add_label)
    # 发起请求
    res = requests.put(add_label)
    

# 定义一个 merge 函数
@track_cronjob_execution('auto_merge')
def merge(source_branch="release",target_branch="master"):
    # 定义基础的 URL
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    # 打开 JSON 文件
    json_path = os.path.join(BASE_DIR, 'app01', 'services_id.json')
    with open(json_path) as f:
        # 读取 JSON 数据
        data = json.load(f)
    # 将 JSON 数据赋值给 mrMap
    mrMap = data
    # 定义标题
    title = "[QA auto merge]cronjob From {} to {}".format(source_branch,target_branch)
    # 定义黑名单,其中63066是 cs 仓库，泽林那边不用 release 导致代码不一致。
    black_list = [9674, 19635, 44700, 48901, 85497, 32154,63066,44163]
    
    # 遍历 mrMap
    for repo in mrMap.values():
        # 如果 repo 在黑名单中，则跳过
        if repo in black_list:
            continue
        # 拼接 URL
        real_url = f"{BASE_URL}{repo}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN_OF_LIANG}&source_branch={source_branch}&target_branch={target_branch}&title={title}"
        ic(real_url)
        #format(repo_id=repo, title=title)
        # 发起请求
        get_feedback_isue = requests.post(real_url)
        # 将响应结果转换为 JSON 数据
        text_all = json.loads(get_feedback_isue.text)
        # 如果响应结果中没有 iid，则跳过
        if "iid" not in text_all.keys():
            continue
        # 获取 iid
        iid = text_all["iid"]
        # 打印出 iid
        ic(iid)
        # 调用 add_MR_label 函数，给 merge request 添加标签
        add_MR_label(repo, iid)
        # 定义计数器
        count = 0
        count_mr = 0
        # 定义 single_url
        single_url = f"{BASE_URL}{repo}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN_OF_LIANG}"
        # 如果响应结果中的 changes_count 为 0 或者为 None，则循环检查 merge request 状态
        if text_all["changes_count"] == "0" or text_all["changes_count"] is None:
            while count <= 15:
                # 发起请求
                single_post = requests.get(single_url)
                # 获取 merge_status
                new_status = json.loads(single_post.text)["merge_status"]
                # 如果 merge_status 不为 checking，则关闭 merge request
                if new_status != "checking":
                    close_url = f"{BASE_URL}{repo}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN_OF_LIANG}&state_event=close"
                    # 发起请求
                    put_close = requests.put(close_url)
                    ic("当变更数量为0时关闭MR:",put_close.status_code)
                    # 跳出循环
                    break
                else:
                    # 兼容安全扫描
                    time.sleep(30) #兼容安全扫描
                    count += 1
        else:
            # 如果 merge request 状态为 cannot_be_merged，则跳出循环
            while count_mr <= 20:
                # 发起请求
                single_post = requests.get(single_url)
                # 获取 merge_status
                new_status = json.loads(single_post.text)["merge_status"]
                
                # 如果 merge_status 为 cannot_be_merged，则跳出循环
                if new_status == "cannot_be_merged":
                    ic(new_status)
                    break
                # 如果 merge_status 为 can_be_merged，则发起请求
                if new_status == "can_be_merged":
                    # 发起请求,用liang.tang的账号 approve MR
                    approval_rules = f"{BASE_URL}{repo}/merge_requests/{iid}/approve?private_token={GITLAB_PRIVATE_TOKEN_OF_ZHULI}"
                    # 发起请求
                    ic(approval_rules)
                    approval_post = requests.post(approval_rules)
                    ic("发起请求，approve MR：",approval_post.status_code)
                    # 定义计数器
                    single_count_mr = 0
                    # 循环检查 merge request 状态
                    while single_count_mr < 15:
                        # 发起请求
                        merge_url = f"{BASE_URL}{repo}/merge_requests/{iid}/merge?private_token={GITLAB_PRIVATE_TOKEN_OF_LIANG}"
                        # 发起请求
                        ic("发起合并请求",merge_url)
                        put_merge = requests.put(merge_url)
                        ic(put_merge.status_code)
                        # 如果响应状态码在指定范围内，则跳出循环
                        if str(put_merge.status_code) in ["200", "201", "202", "203", "204", "205"]:
                            break
                        else:
                            # 兼容安全扫描
                            time.sleep(30)
                            single_count_mr += 1
                    # 跳出循环
                    break
                # 如果 merge_status 不为 can_be_merged，则等待 5 秒
                else:
                    time.sleep(5)
                    count_mr += 1


# 调用 merge 函数
if __name__ == '__main__':
    #merge()
    if len(sys.argv) !=3:
        print("请输入参数：<source_branch>,<target_branch>")
        sys.exit(1)
    
    source_branch = sys.argv[1]
    target_branch = sys.argv[2]
    merge(source_branch,target_branch)

#README
#1.此文件运行的时候会把源分支合入目标分支
#2.需要加计划任务执行
#每日早点9点把  release 分支合入 master 分支
# 0 9 * * * python3 -u $BASE_DIR/app01/automergemaster.py release master >> $LOGS_DIR/auto_merge_release_to_master.log 2>&1

#每天下午6点，把master分支合入uat分支
# 0 18 * * * python3 -u $BASE_DIR/app01/automergemaster.py master uat >> $LOGS_DIR/auto_merge_master_to_uat.log 2>&1
