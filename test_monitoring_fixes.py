#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试监控系统修复效果
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

def test_decorator_fixes():
    """测试装饰器修复"""
    print("🔍 测试计划任务装饰器修复")
    print("=" * 60)
    
    try:
        # 检查装饰器是否正确导入
        from app01.statistics.decorators import track_cronjob_execution
        print("✅ 装饰器导入成功")
        
        # 检查一些关键函数是否有装饰器
        from app01.views import failuremsg_final, MRnotdeal, MRnotdealchannel_new
        from app01.automergemaster import merge
        
        functions_to_check = [
            (failuremsg_final, 'failuremsg_final'),
            (MRnotdeal, 'MRnotdeal'),
            (MRnotdealchannel_new, 'MRnotdealchannel_new'),
            (merge, 'merge')
        ]
        
        for func, name in functions_to_check:
            if hasattr(func, '__wrapped__'):
                print(f"✅ {name} 函数已添加装饰器")
            else:
                print(f"⚠️  {name} 函数可能没有装饰器")
        
        return True
        
    except Exception as e:
        print(f"❌ 装饰器测试失败: {e}")
        return False

def test_timezone_fixes():
    """测试时区修复"""
    print("\n🔍 测试时区修复")
    print("=" * 60)
    
    try:
        from app01.statistics.services import RealtimeStatsService
        
        service = RealtimeStatsService()
        logs = service.get_activity_logs(limit=5)
        
        if 'error' in logs:
            print(f"❌ 获取活动日志失败: {logs['error']}")
            return False
        
        if logs and len(logs) > 0:
            print(f"✅ 成功获取 {len(logs)} 条活动日志")
            
            # 检查时间格式
            for log in logs[:2]:
                time_str = log.get('time', '')
                print(f"   📅 时间格式: {time_str}")
                
                # 验证时间格式是否正确
                try:
                    datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    print(f"   ✅ 时间格式正确")
                except:
                    print(f"   ❌ 时间格式错误")
        else:
            print("⚠️  没有活动日志数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 时区测试失败: {e}")
        return False

def test_cronjob_comprehensive_api():
    """测试综合定时任务API"""
    print("\n🔍 测试综合定时任务API")
    print("=" * 60)
    
    try:
        from app01.statistics.services import cronjob_monitoring_service
        
        result = cronjob_monitoring_service.get_comprehensive_cronjob_status(hours=24)
        
        if 'error' in result:
            print(f"❌ API调用失败: {result['error']}")
            return False
        
        summary = result.get('summary', {})
        recent_failures = result.get('recent_failures', [])
        
        print("✅ 综合定时任务API调用成功")
        print(f"📊 统计数据:")
        print(f"   系统运行中任务: {summary.get('running_jobs', 0)}")
        print(f"   用户活跃任务: {summary.get('active_user_tasks', 0)}")
        print(f"   今日成功总数: {summary.get('success_today', 0)}")
        print(f"   - 系统任务成功: {summary.get('system_success', 0)}")
        print(f"   - 用户任务成功: {summary.get('user_success', 0)}")
        print(f"   今日失败总数: {summary.get('failed_today', 0)}")
        print(f"   - 系统任务失败: {summary.get('system_failed', 0)}")
        print(f"   - 用户任务失败: {summary.get('user_failed', 0)}")
        print(f"   成功率: {summary.get('success_rate', 0)}%")
        
        print(f"\n📋 最近失败记录: {len(recent_failures)} 条")
        for i, failure in enumerate(recent_failures[:3]):
            print(f"   {i+1}. {failure.get('job_name', 'Unknown')} ({failure.get('task_type', 'Unknown')}) - {failure.get('start_time', 'Unknown')}")
            error_msg = failure.get('error_message', '')
            if error_msg:
                print(f"      错误: {error_msg[:100]}{'...' if len(error_msg) > 100 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合定时任务API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_query_content_extraction():
    """测试AI查询内容提取"""
    print("\n🔍 测试AI查询内容提取")
    print("=" * 60)
    
    try:
        from app01.statistics.services import RealtimeStatsService
        
        service = RealtimeStatsService()
        
        # 测试不同的AI查询格式
        test_queries = [
            "/ai 今天天气怎么样",
            "/ai翻译这段文字",
            "ai 帮我查询JIRA任务",
            "AI: 什么是机器学习",
            "问: 如何使用这个系统",
            "很长的查询内容" * 10  # 测试长内容截断
        ]
        
        for query in test_queries:
            extracted = service._extract_ai_query_content(query)
            print(f"   原始: {query[:50]}{'...' if len(query) > 50 else ''}")
            print(f"   提取: {extracted}")
            print()
        
        print("✅ AI查询内容提取测试完成")
        return True
        
    except Exception as e:
        print(f"❌ AI查询内容提取测试失败: {e}")
        return False

def test_http_apis():
    """测试HTTP API"""
    print("\n🔍 测试HTTP API")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    apis_to_test = [
        "/api/statistics/realtime/activity-logs/",
        "/api/statistics/cronjobs/comprehensive/",
        "/api/statistics/realtime/dashboard/"
    ]
    
    success_count = 0
    
    for api in apis_to_test:
        try:
            print(f"📡 测试: {api}")
            response = requests.get(f"{base_url}{api}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success', False):
                    print(f"   ✅ 成功")
                    success_count += 1
                else:
                    print(f"   ❌ API返回错误: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ⚠️  连接失败 (服务可能未运行)")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
    
    print(f"\n📊 HTTP API测试结果: {success_count}/{len(apis_to_test)} 成功")
    return success_count > 0

def main():
    """主函数"""
    print("🚀 ChatBot AutoRelease 监控系统修复验证")
    print("=" * 80)
    
    tests = [
        ("计划任务装饰器", test_decorator_fixes),
        ("时区修复", test_timezone_fixes),
        ("AI查询内容提取", test_ai_query_content_extraction),
        ("综合定时任务API", test_cronjob_comprehensive_api),
        ("HTTP API", test_http_apis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有修复验证通过！")
        print("\n📱 建议操作:")
        print("1. 重启Django服务: python manage.py runserver")
        print("2. 访问监控面板: http://your-server:8000/statistics/dashboard/")
        print("3. 检查各项功能是否正常工作")
    else:
        print("⚠️  部分修复可能需要进一步调整")
    
    return passed == len(results)

if __name__ == '__main__':
    main()
